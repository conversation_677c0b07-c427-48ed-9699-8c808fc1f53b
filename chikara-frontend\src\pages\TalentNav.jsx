import leftarrow from "@/assets/images/UI/NavArrows/leftarrow.png";
import rightarrow from "@/assets/images/UI/NavArrows/rightarrow.png";
import StrokedText from "@/components/StrokedText";
import { cn } from "@/lib/utils";
import { Fragment } from "react";
import { Link } from "react-router-dom";

const NavButton = ({ disabled, link, onClick, type }) => {
    return (
        <Link as="button" to={disabled ? "#" : link} onClick={() => (disabled ? null : onClick())}>
            <div className="squareBtnBlueBG relative flex size-14">
                <img
                    src={type === "left" ? leftarrow : rightarrow}
                    alt=""
                    className={cn(
                        "-translate-x-1/2 -translate-y-1/2 absolute top-[40%] left-[45%] h-7 w-5",
                        disabled && "opacity-75 brightness-50"
                    )}
                />
            </div>
        </Link>
    );
};

export default function TalentNav({
    tabs,
    setSelectedTalent,
    talentPoints,
    currentTreePoints,
    isTalentViewer = false,
}) {
    return (
        <ul
            className={cn(
                isTalentViewer ? "rounded-t-xl" : "md:rounded-t-xl",
                "flex h-26 select-none flex-col border-white/10 border-b bg-gray-400 px-6 pt-4 dark:bg-gray-900"
            )}
        >
            <li className="flex min-w-full flex-none select-none justify-between gap-x-6 px-2 text-gray-400 text-sm leading-6">
                {tabs.map((tab) => {
                    if (!tab.current) {
                        return null;
                    }

                    return (
                        <Fragment key={tab.name}>
                            {tab.leftLink ? (
                                <NavButton link={tab.leftLink} type="left" onClick={setSelectedTalent} />
                            ) : (
                                <NavButton disabled type="left" />
                            )}

                            <div
                                key={tab.name}
                                className="mt-1 flex h-fit w-full rounded-lg bg-blue-900 py-2 text-center shadow-2xl"
                            >
                                <a
                                    href={tab.href}
                                    className={"m-auto font-accent text-gray-200 text-lg text-stroke-s-md"}
                                >
                                    <StrokedText>{tab.name}</StrokedText>{" "}
                                    <span className="align-middle font-lili text-base tracking-wider">
                                        ({currentTreePoints ? currentTreePoints : 0})
                                    </span>
                                </a>
                            </div>
                            {tab.rightLink ? (
                                <NavButton link={tab.rightLink} type="right" onClick={setSelectedTalent} />
                            ) : (
                                <NavButton disabled type="right" />
                            )}
                        </Fragment>
                    );
                })}
            </li>
            <p className="mx-auto flex gap-1 text-lg dark:text-slate-200">
                {talentPoints ? talentPoints : 0}{" "}
                <img
                    className="mt-0.5 size-6"
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/talentpoints.png`}
                    alt=""
                />
            </p>
        </ul>
    );
}
