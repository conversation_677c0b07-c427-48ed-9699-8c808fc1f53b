import patchNotes from "@/constants/patchNotes.json";
import { cn } from "@/lib/utils";
import { Disclosure } from "@headlessui/react";
import { ChevronUp } from "lucide-react";
import { useSearchParams } from "react-router-dom";

const UpdateContent = ({ note }) => (
    <div className="my-2 ml-4 list-disc">
        <h3 className="font-semibold text-custom-yellow text-lg">{note.heading}</h3>
        <h2 className="font-semibold text-base text-blue-500 leading-none">{note.h2Heading}</h2>
        {note.subheading && (
            <p className="font-semibold text-black text-sm dark:text-red-400">
                {note.subheading}{" "}
                {note.subheadinglink && (
                    <a target="_blank" rel="noreferrer" className="text-blue-500 underline" href={note.subheadinglink}>
                        {note.subheadinglink}
                    </a>
                )}
            </p>
        )}
        <div className="ml-6">
            {note.content?.map((contentNote, contentIndex) => (
                <li key={contentIndex} className="text-black text-sm dark:text-slate-200">
                    {contentNote}
                </li>
            ))}
            {note.content2?.map((content2Note, content2Index) => (
                <li key={content2Index} className="text-black text-sm dark:text-slate-200">
                    {content2Note}
                </li>
            ))}
        </div>
        {note.footer && <p className="mt-2 font-medium text-sm text-white">{note.footer}</p>}
    </div>
);

const UpdatePanel = ({ update }) => (
    <Disclosure.Panel className="rounded-b-lg border-x border-b p-4 text-gray-500 text-sm shadow-xl dark:border-slate-600 dark:bg-slate-900 dark:text-slate-300">
        <p className="font-semibold text-black text-lg dark:text-white">{update.heading}</p>
        {update.subheading && <p className="my-2 text-black dark:text-slate-300">{update.subheading}</p>}
        <ul>
            {update.patchNotes?.map((note, index) => (
                <UpdateContent key={index} note={note} />
            ))}
        </ul>
        {update.content1Description && <p className="mt-2 text-gray-300">{update.content1Description}</p>}
        {update.subheading2 && (
            <p className="my-2 text-black dark:text-slate-300">
                <br />
                {update.subheading2}
            </p>
        )}
        {update.content2 && (
            <ul>
                {update.content2.map((content2Note, content2Index) => (
                    <li key={content2Index} className="ml-5 list-disc">
                        {content2Note}
                    </li>
                ))}
            </ul>
        )}
        {update.content2Description && <p className="mt-2 text-gray-300">{update.content2Description}</p>}
    </Disclosure.Panel>
);

const UpdateItem = ({ update, isOpen, i }) => (
    <Disclosure as="div" className="my-2" defaultOpen={isOpen}>
        {({ open }) => (
            <>
                <Disclosure.Button
                    className={cn(
                        open ? "rounded-t-lg" : "rounded-lg",
                        i === 0 ? "bg-blue-600" : "bg-indigo-800",
                        "focus-visible:ring/10 flex w-full justify-between px-4 py-3 text-left font-medium text-lg focus:outline-hidden dark:text-slate-200 dark:text-stroke-sm"
                    )}
                >
                    <div className="flex gap-4">
                        <span className="w-28 font-semibold">{update.date}</span>
                        <span className="font-semibold">{update.version}</span>
                        <span className="font-semibold">{update?.summary}</span>
                    </div>
                    <ChevronUp className={`${open ? "rotate-180" : ""} my-auto size-6 text-white`} />
                </Disclosure.Button>
                <UpdatePanel update={update} />
            </>
        )}
    </Disclosure>
);

export default function Updates() {
    const [searchParams] = useSearchParams();
    const id = searchParams.get("id");
    const reversedUpdates = [...patchNotes].reverse();

    return (
        <div className="w-full px-4 text-shadow">
            <div className="mx-auto my-2 w-full max-w-3xl rounded-2xl bg-white p-2 font-body dark:bg-slate-800">
                {reversedUpdates.map((update, i) => (
                    <UpdateItem key={update.id} update={update} isOpen={update.id === parseInt(id)} i={i} />
                ))}
            </div>
        </div>
    );
}
